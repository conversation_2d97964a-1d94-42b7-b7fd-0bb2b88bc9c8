
import React, { useState } from 'react';
import { Activity, Download, Copy, Filter, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';

// Mock data
const mockLogs = [
  {
    id: 1,
    timestamp: '2024-03-15 14:30:25',
    action: 'Admin Login',
    module: 'Authentication',
    status: 'Success',
    message: 'User logged in successfully',
    ip: '*************'
  },
  {
    id: 2,
    timestamp: '2024-03-15 14:28:15',
    action: 'Campaign Create',
    module: 'Campaigns',
    status: 'Success',
    message: 'New campaign "Spring Sale" created',
    ip: '*************'
  },
  {
    id: 3,
    timestamp: '2024-03-15 14:25:10',
    action: 'Template Approval',
    module: 'Templates',
    status: 'Success',
    message: 'Template "Welcome Message" approved',
    ip: '********'
  },
  {
    id: 4,
    timestamp: '2024-03-15 14:22:45',
    action: 'Message Send',
    module: 'WhatsApp',
    status: 'Failed',
    message: 'Failed to send message - Rate limit exceeded',
    ip: '*************'
  },
  {
    id: 5,
    timestamp: '2024-03-15 14:20:30',
    action: 'Plan Upgrade',
    module: 'Billing',
    status: 'Success',
    message: 'Business upgraded to Pro plan',
    ip: '*************'
  },
  {
    id: 6,
    timestamp: '2024-03-15 14:18:20',
    action: 'Automation Trigger',
    module: 'Automation',
    status: 'Success',
    message: 'Welcome automation triggered for new contact',
    ip: '*************'
  },
  {
    id: 7,
    timestamp: '2024-03-15 14:15:55',
    action: 'Contact Import',
    module: 'Contacts',
    status: 'Failed',
    message: 'CSV import failed - Invalid phone format',
    ip: '*************'
  },
  {
    id: 8,
    timestamp: '2024-03-15 14:12:30',
    action: 'Template Submit',
    module: 'Templates',
    status: 'Success',
    message: 'New template submitted for approval',
    ip: '*************'
  }
];

const SuperAdminLogs: React.FC = () => {
  const [logs, setLogs] = useState(mockLogs);
  const [filterModule, setFilterModule] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredLogs = logs.filter(log => {
    const matchesModule = filterModule === 'all' || log.module.toLowerCase() === filterModule;
    const matchesStatus = filterStatus === 'all' || log.status.toLowerCase() === filterStatus;
    const matchesSearch = log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.message.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesModule && matchesStatus && matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    return status === 'Success' ? (
      <Badge variant="default" className="bg-green-100 text-green-700">
        Success
      </Badge>
    ) : (
      <Badge variant="destructive" className="bg-red-100 text-red-700">
        Failed
      </Badge>
    );
  };

  const getModuleBadge = (module: string) => {
    const colors = {
      'Authentication': 'bg-blue-100 text-blue-700',
      'Campaigns': 'bg-purple-100 text-purple-700',
      'Templates': 'bg-teal-100 text-teal-700',
      'WhatsApp': 'bg-green-100 text-green-700',
      'Billing': 'bg-orange-100 text-orange-700',
      'Automation': 'bg-pink-100 text-pink-700',
      'Contacts': 'bg-indigo-100 text-indigo-700'
    };
    return (
      <Badge variant="secondary" className={colors[module as keyof typeof colors] || 'bg-gray-100 text-gray-700'}>
        {module}
      </Badge>
    );
  };

  const handleRefresh = () => {
    console.log('Refreshing logs...');
    // TODO: Fetch latest logs from backend
  };

  const handleDownloadLogs = () => {
    console.log('Downloading logs...');
    // TODO: Generate and download CSV file
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    console.log('Copied to clipboard:', text);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Logs</h1>
          <p className="text-gray-600 mt-1">Monitor system activities and troubleshoot issues</p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleDownloadLogs} className="bg-teal-600 hover:bg-teal-700">
            <Download className="h-4 w-4 mr-2" />
            Download CSV
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{logs.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-green-600">Successful</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {logs.filter(l => l.status === 'Success').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-red-600">Failed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {logs.filter(l => l.status === 'Failed').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Last Hour</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {logs.filter(l => {
                const logTime = new Date(l.timestamp);
                const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
                return logTime > oneHourAgo;
              }).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Activity Logs
          </CardTitle>
          <CardDescription>
            Real-time system activity and error tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <Input
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="sm:w-64"
            />
            <select
              value={filterModule}
              onChange={(e) => setFilterModule(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Modules</option>
              <option value="authentication">Authentication</option>
              <option value="campaigns">Campaigns</option>
              <option value="templates">Templates</option>
              <option value="whatsapp">WhatsApp</option>
              <option value="billing">Billing</option>
              <option value="automation">Automation</option>
              <option value="contacts">Contacts</option>
            </select>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Status</option>
              <option value="success">Success</option>
              <option value="failed">Failed</option>
            </select>
          </div>

          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Module</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Message</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map((log) => (
                  <TableRow key={log.id} className="hover:bg-gray-50 transition-colors">
                    <TableCell className="font-mono text-xs text-gray-600">
                      {log.timestamp}
                    </TableCell>
                    <TableCell className="font-medium">{log.action}</TableCell>
                    <TableCell>{getModuleBadge(log.module)}</TableCell>
                    <TableCell>{getStatusBadge(log.status)}</TableCell>
                    <TableCell className="max-w-xs truncate text-gray-600">
                      {log.message}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(`${log.timestamp} - ${log.action}: ${log.message}`)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredLogs.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No logs found matching your filters.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SuperAdminLogs;
