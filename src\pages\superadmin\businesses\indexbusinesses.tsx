import React, { useState } from 'react';
import { Building2, Filter, Search, Eye, Calendar, TrendingUp, MessageSquare } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import HeaderCard from './headercard';

import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
export type Business = {
    id: number;
    name: string;
    signupDate: string;
    plan: string;
    campaigns: number;
    messages: number;
    status: string;
    automations: number;
    topCategory: string;
    usage: number;
};

// Mock data
// Export mockBusinesses for possible reuse
export const mockBusinesses: Business[] = [
  {
    id: 1,
    name: 'TechCorp Solutions',
    signupDate: '2024-01-15',
    plan: 'Enterprise',
    campaigns: 45,
    messages: 12500,
    status: 'Active',
    automations: 8,
    topCategory: 'Marketing',
    usage: 85
  },
  {
    id: 2,
    name: 'Digital Marketing Pro',
    signupDate: '2024-02-10',
    plan: 'Pro',
    campaigns: 23,
    messages: 5800,
    status: 'Active',
    automations: 5,
    topCategory: 'Sales',
    usage: 67
  },
  {
    id: 3,
    name: 'StartupXYZ',
    signupDate: '2024-03-05',
    plan: 'Starter',
    campaigns: 8,
    messages: 1200,
    status: 'Inactive',
    automations: 2,
    topCategory: 'Support',
    usage: 34
  },
  {
    id: 4,
    name: 'Global Retail Co',
    signupDate: '2024-01-28',
    plan: 'Enterprise',
    campaigns: 67,
    messages: 18900,
    status: 'Active',
    automations: 12,
    topCategory: 'E-commerce',
    usage: 92
  }
];

const SuperAdminBusinesses: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPlan, setFilterPlan] = useState('all');
  const [selectedBusiness, setSelectedBusiness] = useState<any>(null);

  const filteredBusinesses = mockBusinesses.filter(business => {
    const matchesSearch = business.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPlan = filterPlan === 'all' || business.plan.toLowerCase() === filterPlan;
    return matchesSearch && matchesPlan;
  });

  const getStatusBadge = (status: string) => {
    return status === 'Active' ? (
      <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">
        Active
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-gray-100 text-gray-600">
        Inactive
      </Badge>
    );
  };

  const getPlanBadge = (plan: string) => {
    const colors = {
      'Free': 'bg-gray-100 text-gray-700',
      'Starter': 'bg-blue-100 text-blue-700',
      'Pro': 'bg-teal-100 text-teal-700',
      'Enterprise': 'bg-purple-100 text-purple-700'
    };
    return (
      <Badge variant="secondary" className={colors[plan as keyof typeof colors] || 'bg-gray-100 text-gray-700'}>
        {plan}
      </Badge>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Business Accounts</h1>
          <p className="text-gray-600 mt-1">Monitor and manage all businesses on the platform</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search businesses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <select
            value={filterPlan}
            onChange={(e) => setFilterPlan(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Plans</option>
            <option value="free">Free</option>
            <option value="starter">Starter</option>
            <option value="pro">Pro</option>
            <option value="enterprise">Enterprise</option>
          </select>
        </div>
      </div>

      {/* Stats Cards */}

      {/* Businesses Table */}
      <HeaderCard 
        filteredBusinesses={filteredBusinesses}
        getPlanBadge={getPlanBadge}
        getStatusBadge={getStatusBadge}
        setSelectedBusiness={setSelectedBusiness}
      />

    </div>
  );
};

export default SuperAdminBusinesses;
