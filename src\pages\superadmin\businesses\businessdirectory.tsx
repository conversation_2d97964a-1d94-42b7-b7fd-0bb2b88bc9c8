import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface BusinessDirectoryProps {
  mockBusinesses: Array<{
    id: number;
    name: string;
    signupDate: string;
    plan: string;
    campaigns: number;
    messages: number;
    status: string;
    automations: number;
    topCategory: string;
    usage: number;
  }>;
}

const BusinessDirectory: React.FC<BusinessDirectoryProps> = ({ mockBusinesses }) => (
  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">Total Businesses</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900">{mockBusinesses.length}</div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">Active Businesses</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-green-600">
          {mockBusinesses.filter(b => b.status === 'Active').length}
        </div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">Total Messages</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-blue-600">
          {mockBusinesses.reduce((sum, b) => sum + b.messages, 0).toLocaleString()}
        </div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">Total Campaigns</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-purple-600">
          {mockBusinesses.reduce((sum, b) => sum + b.campaigns, 0)}
        </div>
      </CardContent>
    </Card>
  </div>
);

export default BusinessDirectory;