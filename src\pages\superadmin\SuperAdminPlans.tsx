
import React, { useState } from 'react';
import { CreditCard, Plus, Edit, Trash2, Check, X } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

// Mock data
const mockPlans = [
  {
    id: 1,
    name: 'Free',
    price: 0,
    messageQuota: 100,
    automationLimit: 1,
    contactLimit: 100,
    features: ['Basic Templates', 'Manual Campaigns'],
    isActive: true,
    subscribers: 45
  },
  {
    id: 2,
    name: 'Starter',
    price: 999,
    messageQuota: 1000,
    automationLimit: 5,
    contactLimit: 1000,
    features: ['All Templates', 'Automation', 'Analytics', 'Support'],
    isActive: true,
    subscribers: 32
  },
  {
    id: 3,
    name: 'Pro',
    price: 2999,
    messageQuota: 5000,
    automationLimit: 20,
    contactLimit: 10000,
    features: ['Everything in Starter', 'Advanced Analytics', 'API Access', 'Priority Support'],
    isActive: true,
    subscribers: 18
  },
  {
    id: 4,
    name: 'Enterprise',
    price: 9999,
    messageQuota: 25000,
    automationLimit: 100,
    contactLimit: 100000,
    features: ['Everything in Pro', 'Custom Integrations', 'Dedicated Support', 'SLA'],
    isActive: true,
    subscribers: 8
  }
];

const SuperAdminPlans: React.FC = () => {
  const [plans, setPlans] = useState(mockPlans);
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    price: 0,
    messageQuota: 0,
    automationLimit: 0,
    contactLimit: 0,
    features: [] as string[],
    isActive: true
  });

  const handleEditPlan = (plan: any) => {
    setSelectedPlan(plan);
    setFormData({
      name: plan.name,
      price: plan.price,
      messageQuota: plan.messageQuota,
      automationLimit: plan.automationLimit,
      contactLimit: plan.contactLimit,
      features: [...plan.features],
      isActive: plan.isActive
    });
    setIsEditing(true);
  };

  const handleCreatePlan = () => {
    setSelectedPlan(null);
    setFormData({
      name: '',
      price: 0,
      messageQuota: 0,
      automationLimit: 0,
      contactLimit: 0,
      features: [],
      isActive: true
    });
    setIsEditing(true);
  };

  const handleSavePlan = () => {
    if (selectedPlan) {
      // Update existing plan
      setPlans(plans.map(p => p.id === selectedPlan.id ? { ...selectedPlan, ...formData } : p));
      console.log('Updated plan:', { ...selectedPlan, ...formData });
    } else {
      // Create new plan
      const newPlan = { ...formData, id: plans.length + 1, subscribers: 0 };
      setPlans([...plans, newPlan]);
      console.log('Created new plan:', newPlan);
    }
    // TODO: Save to backend
    setIsEditing(false);
  };

  const handleTogglePlanStatus = (planId: number) => {
    setPlans(plans.map(plan => 
      plan.id === planId ? { ...plan, isActive: !plan.isActive } : plan
    ));
    console.log(`Toggled plan ${planId} status`);
    // TODO: Update backend
  };

  const addFeature = (feature: string) => {
    if (feature.trim() && !formData.features.includes(feature)) {
      setFormData({
        ...formData,
        features: [...formData.features, feature]
      });
    }
  };

  const removeFeature = (index: number) => {
    setFormData({
      ...formData,
      features: formData.features.filter((_, i) => i !== index)
    });
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Subscription Plans</h1>
          <p className="text-gray-600 mt-1">Manage pricing plans and features for the platform</p>
        </div>
        <Button onClick={handleCreatePlan} className="bg-teal-600 hover:bg-teal-700">
          <Plus className="h-4 w-4 mr-2" />
          Create New Plan
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Plans</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{plans.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Active Plans</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {plans.filter(p => p.isActive).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Subscribers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {plans.reduce((sum, p) => sum + p.subscribers, 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Monthly Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              ₹{plans.reduce((sum, p) => sum + (p.price * p.subscribers), 0).toLocaleString()}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {plans.map((plan) => (
          <Card key={plan.id} className={`relative ${!plan.isActive ? 'opacity-60' : ''}`}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{plan.name}</CardTitle>
                <Badge variant={plan.isActive ? "default" : "secondary"}>
                  {plan.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div className="text-3xl font-bold text-teal-600">
                ₹{plan.price.toLocaleString()}
                <span className="text-sm font-normal text-gray-600">/month</span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Messages</span>
                  <span className="font-medium">{plan.messageQuota.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Automations</span>
                  <span className="font-medium">{plan.automationLimit}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Contacts</span>
                  <span className="font-medium">{plan.contactLimit.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subscribers</span>
                  <span className="font-medium text-blue-600">{plan.subscribers}</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-700">Features:</div>
                <ul className="space-y-1">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="text-xs text-gray-600 flex items-center gap-1">
                      <Check className="h-3 w-3 text-green-500" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditPlan(plan)}
                  className="flex-1"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Edit
                </Button>
                <Button
                  variant={plan.isActive ? "destructive" : "default"}
                  size="sm"
                  onClick={() => handleTogglePlanStatus(plan.id)}
                  className="flex-1"
                >
                  {plan.isActive ? (
                    <>
                      <X className="h-3 w-3 mr-1" />
                      Deactivate
                    </>
                  ) : (
                    <>
                      <Check className="h-3 w-3 mr-1" />
                      Activate
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit/Create Plan Dialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>
              {selectedPlan ? 'Edit Plan' : 'Create New Plan'}
            </DialogTitle>
            <DialogDescription>
              Configure plan details and features
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Plan Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="e.g., Pro"
                />
              </div>
              <div>
                <Label htmlFor="price">Price (₹/month)</Label>
                <Input
                  id="price"
                  type="number"
                  value={formData.price}
                  onChange={(e) => setFormData({...formData, price: parseInt(e.target.value) || 0})}
                  placeholder="2999"
                />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="messages">Messages</Label>
                <Input
                  id="messages"
                  type="number"
                  value={formData.messageQuota}
                  onChange={(e) => setFormData({...formData, messageQuota: parseInt(e.target.value) || 0})}
                  placeholder="5000"
                />
              </div>
              <div>
                <Label htmlFor="automations">Automations</Label>
                <Input
                  id="automations"
                  type="number"
                  value={formData.automationLimit}
                  onChange={(e) => setFormData({...formData, automationLimit: parseInt(e.target.value) || 0})}
                  placeholder="20"
                />
              </div>
              <div>
                <Label htmlFor="contacts">Contacts</Label>
                <Input
                  id="contacts"
                  type="number"
                  value={formData.contactLimit}
                  onChange={(e) => setFormData({...formData, contactLimit: parseInt(e.target.value) || 0})}
                  placeholder="10000"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({...formData, isActive: checked})}
              />
              <Label htmlFor="active">Plan is active</Label>
            </div>

            <div>
              <Label>Features</Label>
              <div className="space-y-2 mt-2">
                {formData.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="text-sm bg-gray-100 px-2 py-1 rounded flex-1">{feature}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFeature(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
                <Input
                  placeholder="Add feature and press Enter"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      addFeature((e.target as HTMLInputElement).value);
                      (e.target as HTMLInputElement).value = '';
                    }
                  }}
                />
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Button onClick={handleSavePlan} className="flex-1">
                {selectedPlan ? 'Update Plan' : 'Create Plan'}
              </Button>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SuperAdminPlans;
