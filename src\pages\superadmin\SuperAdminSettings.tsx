
import React, { useState } from 'react';
import { Settings, Upload, Save, Globe, Mail, Shield } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';

const SuperAdminSettings: React.FC = () => {
  const [settings, setSettings] = useState({
    platformName: 'AyuChat',
    supportEmail: '<EMAIL>',
    signupEnabled: true,
    maintenanceMode: false,
    timezone: 'Asia/Kolkata',
    currency: 'INR',
    logo: null as File | null,
    notifications: {
      newSignups: true,
      templateSubmissions: true,
      systemErrors: true,
      billingIssues: true
    },
    welcomeMessage: 'Welcome to AyuChat! Start your WhatsApp marketing journey today.',
    termsUrl: 'https://ayuchat.com/terms',
    privacyUrl: 'https://ayuchat.com/privacy'
  });

  const [isSaving, setIsSaving] = useState(false);

  const handleInputChange = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNotificationChange = (field: string, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [field]: value
      }
    }));
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSettings(prev => ({ ...prev, logo: file }));
    }
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    console.log('Saving settings:', settings);
    // TODO: Save to backend
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      console.log('Settings saved successfully');
    }, 1000);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Platform Settings</h1>
          <p className="text-gray-600 mt-1">Configure global platform preferences and branding</p>
        </div>
        <Button 
          onClick={handleSaveSettings} 
          disabled={isSaving}
          className="bg-teal-600 hover:bg-teal-700"
        >
          <Save className="h-4 w-4 mr-2" />
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Platform Branding */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Platform Branding
            </CardTitle>
            <CardDescription>
              Configure your platform's identity and branding
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="platformName">Platform Name</Label>
              <Input
                id="platformName"
                value={settings.platformName}
                onChange={(e) => handleInputChange('platformName', e.target.value)}
                placeholder="AyuChat"
              />
            </div>

            <div>
              <Label htmlFor="supportEmail">Support Email</Label>
              <Input
                id="supportEmail"
                type="email"
                value={settings.supportEmail}
                onChange={(e) => handleInputChange('supportEmail', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <Label htmlFor="logo">Platform Logo</Label>
              <div className="mt-1">
                <input
                  id="logo"
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('logo')?.click()}
                  className="w-full"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  {settings.logo ? settings.logo.name : 'Upload Logo'}
                </Button>
              </div>
              {settings.logo && (
                <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-600">
                  Selected: {settings.logo.name}
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="welcomeMessage">Welcome Message</Label>
              <Textarea
                id="welcomeMessage"
                value={settings.welcomeMessage}
                onChange={(e) => handleInputChange('welcomeMessage', e.target.value)}
                placeholder="Welcome message for new users..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* System Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              System Configuration
            </CardTitle>
            <CardDescription>
              Global system settings and preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="timezone">Timezone</Label>
                <select
                  id="timezone"
                  value={settings.timezone}
                  onChange={(e) => handleInputChange('timezone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
                  <option value="UTC">UTC</option>
                  <option value="America/New_York">America/New_York (EST)</option>
                  <option value="Europe/London">Europe/London (GMT)</option>
                </select>
              </div>
              <div>
                <Label htmlFor="currency">Currency</Label>
                <select
                  id="currency"
                  value={settings.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="INR">INR (₹)</option>
                  <option value="USD">USD ($)</option>
                  <option value="EUR">EUR (€)</option>
                  <option value="GBP">GBP (£)</option>
                </select>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="signupEnabled">Enable New Signups</Label>
                  <p className="text-sm text-gray-600">Allow new businesses to register</p>
                </div>
                <Switch
                  id="signupEnabled"
                  checked={settings.signupEnabled}
                  onCheckedChange={(checked) => handleInputChange('signupEnabled', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                  <p className="text-sm text-gray-600">Put platform under maintenance</p>
                </div>
                <Switch
                  id="maintenanceMode"
                  checked={settings.maintenanceMode}
                  onCheckedChange={(checked) => handleInputChange('maintenanceMode', checked)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="termsUrl">Terms of Service URL</Label>
                <Input
                  id="termsUrl"
                  value={settings.termsUrl}
                  onChange={(e) => handleInputChange('termsUrl', e.target.value)}
                  placeholder="https://ayuchat.com/terms"
                />
              </div>
              <div>
                <Label htmlFor="privacyUrl">Privacy Policy URL</Label>
                <Input
                  id="privacyUrl"
                  value={settings.privacyUrl}
                  onChange={(e) => handleInputChange('privacyUrl', e.target.value)}
                  placeholder="https://ayuchat.com/privacy"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Notification Settings
            </CardTitle>
            <CardDescription>
              Configure which events trigger notifications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>New Business Signups</Label>
                <p className="text-sm text-gray-600">Get notified when new businesses register</p>
              </div>
              <Switch
                checked={settings.notifications.newSignups}
                onCheckedChange={(checked) => handleNotificationChange('newSignups', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>Template Submissions</Label>
                <p className="text-sm text-gray-600">Get notified when templates are submitted</p>
              </div>
              <Switch
                checked={settings.notifications.templateSubmissions}
                onCheckedChange={(checked) => handleNotificationChange('templateSubmissions', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>System Errors</Label>
                <p className="text-sm text-gray-600">Get notified about critical system errors</p>
              </div>
              <Switch
                checked={settings.notifications.systemErrors}
                onCheckedChange={(checked) => handleNotificationChange('systemErrors', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>Billing Issues</Label>
                <p className="text-sm text-gray-600">Get notified about payment failures</p>
              </div>
              <Switch
                checked={settings.notifications.billingIssues}
                onCheckedChange={(checked) => handleNotificationChange('billingIssues', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security Settings
            </CardTitle>
            <CardDescription>
              Platform security and access controls
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 text-blue-800 font-medium">
                <Shield className="h-4 w-4" />
                Security Status
              </div>
              <div className="text-sm text-blue-700 mt-1">
                Platform security is active. All communications are encrypted.
              </div>
            </div>

            <div className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                Change Super Admin Password
              </Button>
              <Button variant="outline" className="w-full justify-start">
                View Access Logs
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Backup Configuration
              </Button>
            </div>

            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="text-amber-800 font-medium text-sm">
                Warning: Maintenance Mode Active
              </div>
              <div className="text-amber-700 text-xs mt-1">
                Platform is currently under maintenance. Users cannot access the system.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SuperAdminSettings;
