import React, { useState } from "react";
import {
  Plus,
  Search,
  Filter,
  Play,
  Pause,
  Edit,
  Trash2,
  Send,
  Users,
  Calendar,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

// Mock data for campaigns
const mockCampaigns = [
  {
    id: 1,
    name: "Welcome Series",
    template: "Welcome New Customer",
    audience: "New Customers",
    status: "Active",
    scheduled: "2024-03-15 09:00",
    sent: 1250,
    delivered: 1180,
    opened: 890,
    clicked: 234,
    createdAt: "2024-03-14",
    type: "Automated",
  },
  {
    id: 2,
    name: "Product Launch",
    template: "Product Announcement",
    audience: "All Customers",
    status: "Completed",
    scheduled: "2024-03-10 10:00",
    sent: 2100,
    delivered: 2050,
    opened: 1680,
    clicked: 420,
    createdAt: "2024-03-09",
    type: "Broadcast",
  },
  {
    id: 3,
    name: "Holiday Sale",
    template: "Black Friday Sale",
    audience: "VIP Customers",
    status: "Scheduled",
    scheduled: "2024-03-20 08:00",
    sent: 0,
    delivered: 0,
    opened: 0,
    clicked: 0,
    createdAt: "2024-03-16",
    type: "Broadcast",
  },
  {
    id: 4,
    name: "Cart Abandonment",
    template: "Cart Reminder",
    audience: "Abandoned Carts",
    status: "Paused",
    scheduled: "2024-03-12 14:00",
    sent: 156,
    delivered: 149,
    opened: 98,
    clicked: 23,
    createdAt: "2024-03-11",
    type: "Automated",
  },
];

const mockTemplates = [
  { id: 1, name: "Welcome New Customer", category: "Marketing" },
  { id: 2, name: "Product Announcement", category: "Marketing" },
  { id: 3, name: "Black Friday Sale", category: "Marketing" },
  { id: 4, name: "Cart Reminder", category: "Utility" },
  { id: 5, name: "Order Confirmation", category: "Transactional" },
];

const mockAudiences = [
  { id: 1, name: "All Customers", count: 2500 },
  { id: 2, name: "New Customers", count: 450 },
  { id: 3, name: "VIP Customers", count: 180 },
  { id: 4, name: "Abandoned Carts", count: 320 },
  { id: 5, name: "Inactive Users", count: 890 },
];

const AdminCampaigns: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterType, setFilterType] = useState("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const { toast } = useToast();

  // Form state for creating new campaign
  const [newCampaign, setNewCampaign] = useState({
    name: "",
    template: "",
    audience: "",
    type: "Broadcast",
    scheduledDate: "",
    scheduledTime: "",
  });

  const filteredCampaigns = mockCampaigns.filter((campaign) => {
    const matchesSearch =
      campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      campaign.template.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filterStatus === "all" || campaign.status === filterStatus;
    const matchesType = filterType === "all" || campaign.type === filterType;

    return matchesSearch && matchesStatus && matchesType;
  });

  const handleCreateCampaign = () => {
    // TODO: Implement API call to create campaign
    toast({
      title: "Campaign Created",
      description: "Your campaign has been created successfully.",
    });
    setIsCreateDialogOpen(false);
    setNewCampaign({
      name: "",
      template: "",
      audience: "",
      type: "Broadcast",
      scheduledDate: "",
      scheduledTime: "",
    });
  };

  const handleCampaignAction = (action: string, campaignId: number) => {
    // TODO: Implement campaign actions (play, pause, stop)
    toast({
      title: `Campaign ${action}`,
      description: `Campaign has been ${action.toLowerCase()}d successfully.`,
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return (
          <Badge className="bg-green-100 text-green-800">
            <Play className="w-3 h-3 mr-1" />
            Active
          </Badge>
        );
      case "Completed":
        return (
          <Badge className="bg-blue-100 text-blue-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      case "Scheduled":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            Scheduled
          </Badge>
        );
      case "Paused":
        return (
          <Badge className="bg-gray-100 text-gray-800">
            <Pause className="w-3 h-3 mr-1" />
            Paused
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    return type === "Automated" ? (
      <Badge variant="outline" className="text-purple-600 border-purple-200">
        Automated
      </Badge>
    ) : (
      <Badge variant="outline" className="text-blue-600 border-blue-200">
        Broadcast
      </Badge>
    );
  };

  const calculateOpenRate = (opened: number, delivered: number) => {
    return delivered > 0 ? ((opened / delivered) * 100).toFixed(1) : "0.0";
  };

  const calculateClickRate = (clicked: number, opened: number) => {
    return opened > 0 ? ((clicked / opened) * 100).toFixed(1) : "0.0";
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Campaigns</h1>
          <p className="text-gray-600 mt-1">
            Create and manage your WhatsApp marketing campaigns
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-teal-600 hover:bg-teal-700">
              <Plus className="w-4 h-4 mr-2" />
              Create Campaign
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Campaign</DialogTitle>
              <DialogDescription>
                Set up a new WhatsApp marketing campaign
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="campaign-name">Campaign Name</Label>
                <Input
                  id="campaign-name"
                  value={newCampaign.name}
                  onChange={(e) =>
                    setNewCampaign({ ...newCampaign, name: e.target.value })
                  }
                  placeholder="Enter campaign name"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="template">Template</Label>
                  <Select
                    value={newCampaign.template}
                    onValueChange={(value) =>
                      setNewCampaign({ ...newCampaign, template: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select template" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockTemplates.map((template) => (
                        <SelectItem key={template.id} value={template.name}>
                          {template.name} ({template.category})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="audience">Target Audience</Label>
                  <Select
                    value={newCampaign.audience}
                    onValueChange={(value) =>
                      setNewCampaign({ ...newCampaign, audience: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select audience" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockAudiences.map((audience) => (
                        <SelectItem key={audience.id} value={audience.name}>
                          {audience.name} ({audience.count} contacts)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="type">Campaign Type</Label>
                <Select
                  value={newCampaign.type}
                  onValueChange={(value) =>
                    setNewCampaign({ ...newCampaign, type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Broadcast">
                      Broadcast (One-time)
                    </SelectItem>
                    <SelectItem value="Automated">
                      Automated (Trigger-based)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="date">Schedule Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={newCampaign.scheduledDate}
                    onChange={(e) =>
                      setNewCampaign({
                        ...newCampaign,
                        scheduledDate: e.target.value,
                      })
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="time">Schedule Time</Label>
                  <Input
                    id="time"
                    type="time"
                    value={newCampaign.scheduledTime}
                    onChange={(e) =>
                      setNewCampaign({
                        ...newCampaign,
                        scheduledTime: e.target.value,
                      })
                    }
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateCampaign}
                  className="bg-teal-600 hover:bg-teal-700"
                >
                  Create Campaign
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Campaigns
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockCampaigns.length}
                </p>
              </div>
              <Send className="h-8 w-8 text-teal-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Active Campaigns
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockCampaigns.filter((c) => c.status === "Active").length}
                </p>
              </div>
              <Play className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Messages Sent
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockCampaigns
                    .reduce((sum, c) => sum + c.sent, 0)
                    .toLocaleString()}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Avg. Open Rate
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {(
                    mockCampaigns.reduce(
                      (sum, c) =>
                        sum +
                        (c.delivered > 0 ? (c.opened / c.delivered) * 100 : 0),
                      0
                    ) / mockCampaigns.length
                  ).toFixed(1)}
                  %
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search campaigns..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="Active">Active</SelectItem>
            <SelectItem value="Completed">Completed</SelectItem>
            <SelectItem value="Scheduled">Scheduled</SelectItem>
            <SelectItem value="Paused">Paused</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All Types" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="Broadcast">Broadcast</SelectItem>
            <SelectItem value="Automated">Automated</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Campaigns Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredCampaigns.map((campaign) => (
          <Card key={campaign.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Send className="w-4 h-4 text-teal-600" />
                    {campaign.name}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    Template: {campaign.template} • Audience:{" "}
                    {campaign.audience}
                  </CardDescription>
                </div>
                <div className="flex flex-col gap-1">
                  {getStatusBadge(campaign.status)}
                  {getTypeBadge(campaign.type)}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Campaign Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-gray-900">
                      {campaign.sent.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600">Sent</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-gray-900">
                      {campaign.delivered.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600">Delivered</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">
                      {calculateOpenRate(campaign.opened, campaign.delivered)}%
                    </p>
                    <p className="text-sm text-gray-600">Open Rate</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">
                      {calculateClickRate(campaign.clicked, campaign.opened)}%
                    </p>
                    <p className="text-sm text-gray-600">Click Rate</p>
                  </div>
                </div>

                {/* Schedule Info */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    Scheduled: {campaign.scheduled}
                  </span>
                  <span>Created: {campaign.createdAt}</span>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  {campaign.status === "Active" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCampaignAction("Pause", campaign.id)}
                    >
                      <Pause className="w-4 h-4 mr-1" />
                      Pause
                    </Button>
                  )}
                  {campaign.status === "Paused" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handleCampaignAction("Resume", campaign.id)
                      }
                    >
                      <Play className="w-4 h-4 mr-1" />
                      Resume
                    </Button>
                  )}
                  {campaign.status === "Scheduled" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCampaignAction("Start", campaign.id)}
                    >
                      <Play className="w-4 h-4 mr-1" />
                      Start Now
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <Edit className="w-4 h-4 mr-1" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm">
                    <BarChart3 className="w-4 h-4 mr-1" />
                    Analytics
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AdminCampaigns;
