import React, { useState } from 'react';
import { CreditCard, DollarSign, Calendar, Download, ChevronUp, ChevronDown, CheckCircle, AlertCircle, BarChart3, ArrowR<PERSON>, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';

// Mock data for billing
const mockBillingData = {
  currentPlan: {
    name: 'Pro',
    price: 29.99,
    billingCycle: 'monthly',
    nextBillingDate: '2024-04-15',
    status: 'active',
    features: [
      'Up to 5,000 messages per month',
      '20 automation workflows',
      'Advanced analytics',
      'Priority support',
      'API access'
    ]
  },
  usage: {
    messages: {
      used: 3250,
      total: 5000,
      percentage: 65
    },
    automations: {
      used: 12,
      total: 20,
      percentage: 60
    },
    contacts: {
      used: 2500,
      total: 10000,
      percentage: 25
    }
  },
  paymentMethod: {
    type: 'credit_card',
    last4: '4242',
    expiryDate: '05/25',
    name: 'John Doe'
  },
  invoices: [
    {
      id: 'INV-001',
      date: '2024-03-15',
      amount: 29.99,
      status: 'paid',
      downloadUrl: '#'
    },
    {
      id: 'INV-002',
      date: '2024-02-15',
      amount: 29.99,
      status: 'paid',
      downloadUrl: '#'
    },
    {
      id: 'INV-003',
      date: '2024-01-15',
      amount: 29.99,
      status: 'paid',
      downloadUrl: '#'
    }
  ]
};

const availablePlans = [
  {
    name: 'Free',
    price: 0,
    billingCycle: 'monthly',
    features: [
      'Up to 100 messages per month',
      '1 automation workflow',
      'Basic analytics',
      'Email support'
    ],
    recommended: false
  },
  {
    name: 'Starter',
    price: 9.99,
    billingCycle: 'monthly',
    features: [
      'Up to 1,000 messages per month',
      '5 automation workflows',
      'Basic analytics',
      'Email support',
      'Template library access'
    ],
    recommended: false
  },
  {
    name: 'Pro',
    price: 29.99,
    billingCycle: 'monthly',
    features: [
      'Up to 5,000 messages per month',
      '20 automation workflows',
      'Advanced analytics',
      'Priority support',
      'API access'
    ],
    recommended: true
  },
  {
    name: 'Enterprise',
    price: 99.99,
    billingCycle: 'monthly',
    features: [
      'Up to 25,000 messages per month',
      'Unlimited automation workflows',
      'Advanced analytics with exports',
      'Dedicated support',
      'API access',
      'Custom integrations',
      'Multi-user access'
    ],
    recommended: false
  }
];

const AdminBilling: React.FC = () => {
  const [showPlans, setShowPlans] = useState(false);
  const { toast } = useToast();

  const handleChangePlan = (planName: string) => {
    toast({
      title: "Plan Change Requested",
      description: `Your request to change to the ${planName} plan has been submitted.`,
    });
    setShowPlans(false);
  };

  const handleUpdatePayment = () => {
    toast({
      title: "Update Payment Method",
      description: "Payment method update form will be shown here.",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="w-3 h-3 mr-1" />Cancelled</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getInvoiceStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Billing</h1>
        <p className="text-gray-600 mt-1">Manage your subscription and payment details</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Current Plan */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Current Plan
            </CardTitle>
            <CardDescription>
              Your current subscription details and usage
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div>
                <h3 className="text-xl font-bold text-gray-900">{mockBillingData.currentPlan.name} Plan</h3>
                <p className="text-gray-600">
                  ${mockBillingData.currentPlan.price}/{mockBillingData.currentPlan.billingCycle}
                </p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(mockBillingData.currentPlan.status)}
                <Button
                  variant="outline"
                  onClick={() => setShowPlans(!showPlans)}
                >
                  Change Plan
                  {showPlans ? <ChevronUp className="ml-2 h-4 w-4" /> : <ChevronDown className="ml-2 h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div>
              <p className="text-sm text-gray-600 mb-2">Next billing date: {mockBillingData.currentPlan.nextBillingDate}</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Plan Features</h4>
                  <ul className="space-y-1">
                    {mockBillingData.currentPlan.features.map((feature, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Current Usage</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>Messages</span>
                        <span>{mockBillingData.usage.messages.used} / {mockBillingData.usage.messages.total}</span>
                      </div>
                      <Progress value={mockBillingData.usage.messages.percentage} className="h-2" />
                    </div>
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>Automations</span>
                        <span>{mockBillingData.usage.automations.used} / {mockBillingData.usage.automations.total}</span>
                      </div>
                      <Progress value={mockBillingData.usage.automations.percentage} className="h-2" />
                    </div>
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>Contacts</span>
                        <span>{mockBillingData.usage.contacts.used} / {mockBillingData.usage.contacts.total}</span>
                      </div>
                      <Progress value={mockBillingData.usage.contacts.percentage} className="h-2" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Available Plans (Collapsible) */}
            {showPlans && (
              <div className="mt-4 border rounded-lg p-4 space-y-4">
                <h3 className="font-medium text-gray-900">Available Plans</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {availablePlans.map((plan) => (
                    <Card key={plan.name} className={`border ${plan.recommended ? 'border-teal-500' : 'border-gray-200'}`}>
                      <CardHeader className="pb-2">
                        {plan.recommended && (
                          <Badge className="bg-teal-100 text-teal-800 self-start mb-1">Recommended</Badge>
                        )}
                        <CardTitle className="text-lg">{plan.name}</CardTitle>
                        <CardDescription>
                          ${plan.price}/{plan.billingCycle}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <ul className="space-y-1 text-sm">
                          {plan.features.map((feature, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                        <Button
                          variant={plan.name === mockBillingData.currentPlan.name ? 'outline' : 'default'}
                          className={plan.name === mockBillingData.currentPlan.name ? '' : 'bg-teal-600 hover:bg-teal-700'}
                          disabled={plan.name === mockBillingData.currentPlan.name}
                          onClick={() => handleChangePlan(plan.name)}
                          size="sm"
                          className="w-full"
                        >
                          {plan.name === mockBillingData.currentPlan.name ? 'Current Plan' : 'Select Plan'}
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payment Method */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Method
            </CardTitle>
            <CardDescription>
              Manage your payment details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-gray-600" />
                  <span className="font-medium">•••• {mockBillingData.paymentMethod.last4}</span>
                </div>
                <Badge variant="outline">Default</Badge>
              </div>
              <p className="text-sm text-gray-600">Expires {mockBillingData.paymentMethod.expiryDate}</p>
              <p className="text-sm text-gray-600">{mockBillingData.paymentMethod.name}</p>
            </div>
            <Button variant="outline" onClick={handleUpdatePayment} className="w-full">
              Update Payment Method
            </Button>
          </CardContent>
        </Card>

        {/* Invoices */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Billing History
            </CardTitle>
            <CardDescription>
              View and download your past invoices
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Invoice</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Amount</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {mockBillingData.invoices.map((invoice) => (
                    <tr key={invoice.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">{invoice.id}</td>
                      <td className="py-3 px-4">{invoice.date}</td>
                      <td className="py-3 px-4">${invoice.amount}</td>
                      <td className="py-3 px-4">{getInvoiceStatusBadge(invoice.status)}</td>
                      <td className="py-3 px-4 text-right">
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminBilling;
