
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import AdminLayout from "@/layouts/AdminLayout";
import SuperAdminLayout from "@/layouts/SuperAdminLayout";
import ProtectedRoute from "@/components/common/ProtectedRoute";
import LoginForm from "@/components/auth/LoginForm";
import AdminDashboard from "@/pages/admin/AdminDashboard";
import SuperAdminDashboard from "@/pages/superadmin/SuperAdminDashboard";
import SuperAdminBusinesses from "@/pages/superadmin/SuperAdminBusinesses";
import SuperAdminTemplates from "@/pages/superadmin/SuperAdminTemplates";
import SuperAdminAnalytics from "@/pages/superadmin/SuperAdminAnalytics";
import SuperAdminPlans from "@/pages/superadmin/SuperAdminPlans";
import SuperAdminLogs from "@/pages/superadmin/SuperAdminLogs";
import SuperAdminSettings from "@/pages/superadmin/SuperAdminSettings";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          {/* Root redirect */}
          <Route path="/" element={<Navigate to="/admin/login" replace />} />
          
          {/* Admin Routes */}
          <Route path="/admin/login" element={<LoginForm role="admin" />} />
          <Route path="/admin" element={
            <ProtectedRoute requiredRole="admin">
              <AdminLayout />
            </ProtectedRoute>
          }>
            <Route path="dashboard" element={<AdminDashboard />} />
            <Route path="inbox" element={<div className="p-6"><h1 className="text-2xl font-bold">Inbox - Coming Soon</h1><p className="text-gray-600 mt-2">WhatsApp message management interface will be implemented here.</p></div>} />
            <Route path="campaigns" element={<div className="p-6"><h1 className="text-2xl font-bold">Campaigns - Coming Soon</h1><p className="text-gray-600 mt-2">Campaign creation and management tools will be implemented here.</p></div>} />
            <Route path="contacts" element={<div className="p-6"><h1 className="text-2xl font-bold">Contacts - Coming Soon</h1><p className="text-gray-600 mt-2">Contact management and segmentation tools will be implemented here.</p></div>} />
            <Route path="automation" element={<div className="p-6"><h1 className="text-2xl font-bold">Automation - Coming Soon</h1><p className="text-gray-600 mt-2">Workflow builder and automation management will be implemented here.</p></div>} />
            <Route path="templates" element={<div className="p-6"><h1 className="text-2xl font-bold">Templates - Coming Soon</h1><p className="text-gray-600 mt-2">WhatsApp template management will be implemented here.</p></div>} />
            <Route path="phone-numbers" element={<div className="p-6"><h1 className="text-2xl font-bold">Phone Numbers - Coming Soon</h1><p className="text-gray-600 mt-2">WhatsApp Business phone number management will be implemented here.</p></div>} />
            <Route path="analytics" element={<div className="p-6"><h1 className="text-2xl font-bold">Analytics - Coming Soon</h1><p className="text-gray-600 mt-2">Detailed analytics and reporting will be implemented here.</p></div>} />
            <Route path="billing" element={<div className="p-6"><h1 className="text-2xl font-bold">Billing - Coming Soon</h1><p className="text-gray-600 mt-2">Subscription and billing management will be implemented here.</p></div>} />
            <Route path="settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Settings - Coming Soon</h1><p className="text-gray-600 mt-2">Account settings and preferences will be implemented here.</p></div>} />
            <Route index element={<Navigate to="dashboard" replace />} />
          </Route>

          {/* Super Admin Routes */}
          <Route path="/superadmin/login" element={<LoginForm role="superadmin" />} />
          <Route path="/superadmin" element={
            <ProtectedRoute requiredRole="superadmin">
              <SuperAdminLayout />
            </ProtectedRoute>
          }>
            <Route path="dashboard" element={<SuperAdminDashboard />} />
            <Route path="businesses" element={<SuperAdminBusinesses />} />
            <Route path="templates" element={<SuperAdminTemplates />} />
            <Route path="analytics" element={<SuperAdminAnalytics />} />
            <Route path="plans" element={<SuperAdminPlans />} />
            <Route path="logs" element={<SuperAdminLogs />} />
            <Route path="settings" element={<SuperAdminSettings />} />
            <Route index element={<Navigate to="dashboard" replace />} />
          </Route>

          {/* Catch all */}
          <Route path="*" element={<Navigate to="/admin/login" replace />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
