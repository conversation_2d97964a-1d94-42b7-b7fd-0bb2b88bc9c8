import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import AdminLayout from "@/layouts/AdminLayout";
import SuperAdminLayout from "@/layouts/SuperAdminLayout";
import ProtectedRoute from "@/components/common/ProtectedRoute";
import LoginForm from "@/components/auth/LoginForm";
import AdminDashboard from "@/pages/admin/AdminDashboard";
import AdminTemplates from "@/pages/admin/AdminTemplates";
import AdminContacts from "@/pages/admin/AdminContacts";
import AdminCampaigns from "@/pages/admin/AdminCampaigns";
import AdminInbox from "@/pages/admin/AdminInbox";
import AdminAutomation from "@/pages/admin/AdminAutomation";
import AdminPhoneNumbers from "@/pages/admin/AdminPhoneNumbers";
import AdminSettings from "@/pages/admin/AdminSettings";
import AdminBilling from "@/pages/admin/AdminBilling";
import SuperAdminDashboard from "@/pages/superadmin/dashboard/indexdashboard";
import SuperAdminBusinesses from "@/pages/superadmin/businesses/indexbusinesses";
import SuperAdminTemplates from "@/pages/superadmin/templates/indextemplates";
import SuperAdminAnalytics from "@/pages/superadmin/analytics/indexanalytics";
import SuperAdminPlans from "@/pages/superadmin/SuperAdminPlans";
import SuperAdminLogs from "@/pages/superadmin/SuperAdminLogs";
import SuperAdminSettings from "@/pages/superadmin/SuperAdminSettings";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          {/* Root redirect */}
          <Route path="/" element={<Navigate to="/admin/login" replace />} />

          {/* Admin Routes */}
          <Route path="/admin/login" element={<LoginForm role="admin" />} />
          <Route
            path="/admin"
            element={
              <ProtectedRoute requiredRole="admin">
                <AdminLayout />
              </ProtectedRoute>
            }
          >
            <Route path="dashboard" element={<AdminDashboard />} />
            <Route path="inbox" element={<AdminInbox />} />
            <Route path="campaigns" element={<AdminCampaigns />} />
            <Route path="contacts" element={<AdminContacts />} />
            <Route path="automation" element={<AdminAutomation />} />
            <Route path="templates" element={<AdminTemplates />} />
            <Route path="phone-numbers" element={<AdminPhoneNumbers />} />
            <Route
              path="analytics"
              element={
                <div className="p-6">
                  <h1 className="text-2xl font-bold">
                    Analytics - Coming Soon
                  </h1>
                  <p className="text-gray-600 mt-2">
                    Detailed analytics and reporting will be implemented here.
                  </p>
                </div>
              }
            />
            <Route path="billing" element={<AdminBilling />} />
            <Route path="settings" element={<AdminSettings />} />
            <Route index element={<Navigate to="dashboard" replace />} />
          </Route>

          {/* Super Admin Routes */}
          <Route
            path="/superadmin/login"
            element={<LoginForm role="superadmin" />}
          />
          <Route
            path="/superadmin"
            element={
              <ProtectedRoute requiredRole="superadmin">
                <SuperAdminLayout />
              </ProtectedRoute>
            }
          >
            <Route path="dashboard" element={<SuperAdminDashboard />} />
            <Route path="businesses" element={<SuperAdminBusinesses />} />
            <Route path="templates" element={<SuperAdminTemplates />} />
            <Route path="analytics" element={<SuperAdminAnalytics />} />
            <Route path="plans" element={<SuperAdminPlans />} />
            <Route path="logs" element={<SuperAdminLogs />} />
            <Route path="settings" element={<SuperAdminSettings />} />
            <Route index element={<Navigate to="dashboard" replace />} />
          </Route>

          {/* Catch all */}
          <Route path="*" element={<Navigate to="/admin/login" replace />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
