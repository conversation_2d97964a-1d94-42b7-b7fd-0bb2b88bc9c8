import React, { useState } from 'react';
import { Plus, Play, Pause, Edit, Trash2, Zap, MessageSquare, Clock, GitBranch, Tag, LogOut, BarChart3, Save, Settings } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

// Mock data for automation workflows
const mockWorkflows = [
  {
    id: 1,
    name: 'Welcome Series',
    description: 'Automated welcome sequence for new customers',
    status: 'Active',
    trigger: 'New Contact Added',
    totalRuns: 1250,
    completionRate: 85,
    createdAt: '2024-03-10',
    nodes: [
      { id: 'start', type: 'trigger', label: 'New Contact Added', x: 100, y: 100 },
      { id: 'delay1', type: 'delay', label: 'Wait 1 hour', x: 100, y: 200 },
      { id: 'message1', type: 'message', label: 'Welcome Message', x: 100, y: 300 },
      { id: 'delay2', type: 'delay', label: 'Wait 24 hours', x: 100, y: 400 },
      { id: 'message2', type: 'message', label: 'Getting Started Guide', x: 100, y: 500 }
    ]
  },
  {
    id: 2,
    name: 'Cart Abandonment',
    description: 'Recover abandoned shopping carts',
    status: 'Active',
    trigger: 'Cart Abandoned',
    totalRuns: 890,
    completionRate: 67,
    createdAt: '2024-03-12',
    nodes: [
      { id: 'start', type: 'trigger', label: 'Cart Abandoned', x: 100, y: 100 },
      { id: 'delay1', type: 'delay', label: 'Wait 2 hours', x: 100, y: 200 },
      { id: 'message1', type: 'message', label: 'Cart Reminder', x: 100, y: 300 },
      { id: 'condition1', type: 'condition', label: 'Purchase Made?', x: 100, y: 400 },
      { id: 'message2', type: 'message', label: 'Discount Offer', x: 200, y: 500 }
    ]
  },
  {
    id: 3,
    name: 'Birthday Campaign',
    description: 'Send birthday wishes and special offers',
    status: 'Paused',
    trigger: 'Birthday Date',
    totalRuns: 156,
    completionRate: 92,
    createdAt: '2024-03-08',
    nodes: [
      { id: 'start', type: 'trigger', label: 'Birthday Date', x: 100, y: 100 },
      { id: 'message1', type: 'message', label: 'Birthday Wishes', x: 100, y: 200 },
      { id: 'tag1', type: 'tag', label: 'Add Birthday Tag', x: 100, y: 300 }
    ]
  }
];

const nodeTypes = [
  { type: 'trigger', label: 'Trigger', icon: Zap, color: 'bg-purple-100 text-purple-600' },
  { type: 'message', label: 'Send Message', icon: MessageSquare, color: 'bg-blue-100 text-blue-600' },
  { type: 'delay', label: 'Delay', icon: Clock, color: 'bg-yellow-100 text-yellow-600' },
  { type: 'condition', label: 'Condition', icon: GitBranch, color: 'bg-green-100 text-green-600' },
  { type: 'tag', label: 'Add Tag', icon: Tag, color: 'bg-pink-100 text-pink-600' },
  { type: 'exit', label: 'Exit', icon: LogOut, color: 'bg-red-100 text-red-600' }
];

const AdminAutomation: React.FC = () => {
  const [selectedWorkflow, setSelectedWorkflow] = useState<any>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isBuilderOpen, setIsBuilderOpen] = useState(false);
  const { toast } = useToast();

  const [newWorkflow, setNewWorkflow] = useState({
    name: '',
    description: '',
    trigger: ''
  });

  const handleCreateWorkflow = () => {
    toast({
      title: "Workflow Created",
      description: "Your automation workflow has been created successfully.",
    });
    setIsCreateDialogOpen(false);
    setNewWorkflow({ name: '', description: '', trigger: '' });
  };

  const handleWorkflowAction = (action: string, workflowId: number) => {
    toast({
      title: `Workflow ${action}`,
      description: `Workflow has been ${action.toLowerCase()}d successfully.`,
    });
  };

  const openWorkflowBuilder = (workflow: any) => {
    setSelectedWorkflow(workflow);
    setIsBuilderOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Active':
        return <Badge className="bg-green-100 text-green-800"><Play className="w-3 h-3 mr-1" />Active</Badge>;
      case 'Paused':
        return <Badge className="bg-yellow-100 text-yellow-800"><Pause className="w-3 h-3 mr-1" />Paused</Badge>;
      case 'Draft':
        return <Badge className="bg-gray-100 text-gray-800"><Edit className="w-3 h-3 mr-1" />Draft</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const NodeComponent = ({ node }: { node: any }) => {
    const nodeType = nodeTypes.find(nt => nt.type === node.type);
    if (!nodeType) return null;

    const Icon = nodeType.icon;

    return (
      <div
        className={`p-3 rounded-lg border-2 border-dashed border-gray-300 ${nodeType.color} cursor-pointer hover:shadow-md transition-shadow`}
        style={{ position: 'absolute', left: node.x, top: node.y }}
      >
        <div className="flex items-center space-x-2">
          <Icon className="w-4 h-4" />
          <span className="text-sm font-medium">{node.label}</span>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Automation</h1>
          <p className="text-gray-600 mt-1">Create and manage automated WhatsApp workflows</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-teal-600 hover:bg-teal-700">
              <Plus className="w-4 h-4 mr-2" />
              Create Workflow
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Workflow</DialogTitle>
              <DialogDescription>
                Set up a new automation workflow for your WhatsApp campaigns
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="workflow-name">Workflow Name</Label>
                <Input
                  id="workflow-name"
                  value={newWorkflow.name}
                  onChange={(e) => setNewWorkflow({...newWorkflow, name: e.target.value})}
                  placeholder="Enter workflow name"
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newWorkflow.description}
                  onChange={(e) => setNewWorkflow({...newWorkflow, description: e.target.value})}
                  placeholder="Describe what this workflow does"
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="trigger">Trigger Event</Label>
                <Select value={newWorkflow.trigger} onValueChange={(value) => setNewWorkflow({...newWorkflow, trigger: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select trigger event" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="New Contact Added">New Contact Added</SelectItem>
                    <SelectItem value="Tag Added">Tag Added</SelectItem>
                    <SelectItem value="Cart Abandoned">Cart Abandoned</SelectItem>
                    <SelectItem value="Birthday Date">Birthday Date</SelectItem>
                    <SelectItem value="Purchase Made">Purchase Made</SelectItem>
                    <SelectItem value="Form Submitted">Form Submitted</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateWorkflow} className="bg-teal-600 hover:bg-teal-700">
                  Create Workflow
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Workflows</p>
                <p className="text-2xl font-bold text-gray-900">{mockWorkflows.length}</p>
              </div>
              <Zap className="h-8 w-8 text-teal-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Workflows</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockWorkflows.filter(w => w.status === 'Active').length}
                </p>
              </div>
              <Play className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Runs</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockWorkflows.reduce((sum, w) => sum + w.totalRuns, 0).toLocaleString()}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Completion</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Math.round(mockWorkflows.reduce((sum, w) => sum + w.completionRate, 0) / mockWorkflows.length)}%
                </p>
              </div>
              <Settings className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workflows Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {mockWorkflows.map((workflow) => (
          <Card key={workflow.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Zap className="w-4 h-4 text-teal-600" />
                    {workflow.name}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {workflow.description}
                  </CardDescription>
                </div>
                {getStatusBadge(workflow.status)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Workflow Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-lg font-bold text-gray-900">{workflow.totalRuns}</p>
                    <p className="text-sm text-gray-600">Total Runs</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-lg font-bold text-green-600">{workflow.completionRate}%</p>
                    <p className="text-sm text-gray-600">Completion</p>
                  </div>
                </div>

                {/* Trigger Info */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <Zap className="w-4 h-4" />
                    Trigger: {workflow.trigger}
                  </span>
                  <span>Created: {workflow.createdAt}</span>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => openWorkflowBuilder(workflow)}
                    className="flex-1"
                  >
                    <Edit className="w-4 h-4 mr-1" />
                    Edit
                  </Button>
                  {workflow.status === 'Active' ? (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleWorkflowAction('Pause', workflow.id)}
                    >
                      <Pause className="w-4 h-4 mr-1" />
                      Pause
                    </Button>
                  ) : (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleWorkflowAction('Activate', workflow.id)}
                    >
                      <Play className="w-4 h-4 mr-1" />
                      Start
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <BarChart3 className="w-4 h-4 mr-1" />
                    Analytics
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AdminAutomation;
