import React, { useState } from 'react';
import { Plus, Search, Filter, Eye, Edit, Trash2, FileText, MessageSquare, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

// Mock data for system templates (read-only)
const systemTemplates = [
  {
    id: 'sys_1',
    name: 'Welcome New Customer',
    category: 'Marketing',
    type: 'system',
    status: 'approved',
    content: {
      header: 'Welcome to {{business_name}}! 🎉',
      body: 'Hi {{customer_name}}, thank you for choosing our services. We are excited to help you grow your business.',
      footer: '{{business_name}} - Your Growth Partner',
      buttons: ['Get Started', 'Learn More']
    },
    createdAt: '2024-01-15',
    usageCount: 1250
  },
  {
    id: 'sys_2',
    name: 'Order Confirmation',
    category: 'Transactional',
    type: 'system',
    status: 'approved',
    content: {
      header: 'Order Confirmed ✅',
      body: 'Hi {{customer_name}}, your order #{{order_id}} has been confirmed. Total: {{amount}}. Expected delivery: {{delivery_date}}.',
      footer: 'Track your order anytime',
      buttons: ['Track Order', 'Contact Support']
    },
    createdAt: '2024-01-10',
    usageCount: 2100
  },
  {
    id: 'sys_3',
    name: 'Appointment Reminder',
    category: 'Utility',
    type: 'system',
    status: 'approved',
    content: {
      header: 'Appointment Reminder 📅',
      body: 'Hi {{customer_name}}, this is a reminder for your appointment on {{date}} at {{time}}. Location: {{location}}.',
      footer: 'See you soon!',
      buttons: ['Confirm', 'Reschedule']
    },
    createdAt: '2024-01-08',
    usageCount: 890
  }
];

// Mock data for user templates
const mockUserTemplates = [
  {
    id: 'user_1',
    name: 'Black Friday Sale',
    category: 'Marketing',
    type: 'user',
    status: 'pending',
    content: {
      header: 'Black Friday Sale! 🛍️',
      body: 'Hi {{customer_name}}, get up to 50% off on all products. Limited time offer!',
      footer: 'Shop now before it ends',
      buttons: ['Shop Now', 'View Deals']
    },
    createdAt: '2024-03-15',
    usageCount: 0
  },
  {
    id: 'user_2',
    name: 'Payment Reminder',
    category: 'Utility',
    type: 'user',
    status: 'approved',
    content: {
      header: 'Payment Due Reminder 💳',
      body: 'Hi {{customer_name}}, your payment of {{amount}} is due on {{due_date}}. Please make the payment to avoid late fees.',
      footer: 'Thank you for your business',
      buttons: ['Pay Now', 'Contact Us']
    },
    createdAt: '2024-03-10',
    usageCount: 45
  }
];

const AdminTemplates: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const { toast } = useToast();

  // Form state for creating new template
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    category: '',
    header: '',
    body: '',
    footer: '',
    buttons: ['']
  });

  const allTemplates = [...systemTemplates, ...mockUserTemplates];

  const filteredTemplates = allTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.content.body.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || template.category === filterCategory;
    const matchesType = filterType === 'all' || template.type === filterType;
    
    return matchesSearch && matchesCategory && matchesType;
  });

  const handleCreateTemplate = () => {
    // TODO: Implement API call to create template
    toast({
      title: "Template Created",
      description: "Your template has been submitted for approval.",
    });
    setIsCreateDialogOpen(false);
    setNewTemplate({
      name: '',
      category: '',
      header: '',
      body: '',
      footer: '',
      buttons: ['']
    });
  };

  const handlePreviewTemplate = (template: any) => {
    setSelectedTemplate(template);
    setIsPreviewOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Approved</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="w-3 h-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Templates</h1>
          <p className="text-gray-600 mt-1">Manage your WhatsApp message templates</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-teal-600 hover:bg-teal-700">
              <Plus className="w-4 h-4 mr-2" />
              Create Template
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Template</DialogTitle>
              <DialogDescription>
                Create a custom WhatsApp template for your campaigns
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Template Name</Label>
                  <Input
                    id="name"
                    value={newTemplate.name}
                    onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                    placeholder="Enter template name"
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={newTemplate.category} onValueChange={(value) => setNewTemplate({...newTemplate, category: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Marketing">Marketing</SelectItem>
                      <SelectItem value="Transactional">Transactional</SelectItem>
                      <SelectItem value="Utility">Utility</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="header">Header Text</Label>
                <Input
                  id="header"
                  value={newTemplate.header}
                  onChange={(e) => setNewTemplate({...newTemplate, header: e.target.value})}
                  placeholder="Enter header text"
                />
              </div>
              <div>
                <Label htmlFor="body">Body Text</Label>
                <Textarea
                  id="body"
                  value={newTemplate.body}
                  onChange={(e) => setNewTemplate({...newTemplate, body: e.target.value})}
                  placeholder="Enter message body (use {{variable}} for dynamic content)"
                  rows={4}
                />
              </div>
              <div>
                <Label htmlFor="footer">Footer Text</Label>
                <Input
                  id="footer"
                  value={newTemplate.footer}
                  onChange={(e) => setNewTemplate({...newTemplate, footer: e.target.value})}
                  placeholder="Enter footer text"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateTemplate} className="bg-teal-600 hover:bg-teal-700">
                  Create Template
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={filterCategory} onValueChange={setFilterCategory}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="Marketing">Marketing</SelectItem>
            <SelectItem value="Transactional">Transactional</SelectItem>
            <SelectItem value="Utility">Utility</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All Types" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="system">System Templates</SelectItem>
            <SelectItem value="user">My Templates</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg flex items-center gap-2">
                    {template.type === 'system' ? (
                      <FileText className="w-4 h-4 text-blue-600" />
                    ) : (
                      <MessageSquare className="w-4 h-4 text-teal-600" />
                    )}
                    {template.name}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {template.category} • {template.type === 'system' ? 'System' : 'Custom'}
                  </CardDescription>
                </div>
                {getStatusBadge(template.status)}
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                {template.content.body}
              </p>
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span>Used {template.usageCount} times</span>
                <span>{template.createdAt}</span>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePreviewTemplate(template)}
                  className="flex-1"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  Preview
                </Button>
                {template.type === 'user' && (
                  <>
                    <Button variant="outline" size="sm">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Preview Dialog */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Template Preview</DialogTitle>
            <DialogDescription>
              How this template will appear in WhatsApp
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 space-y-3">
              {selectedTemplate.content.header && (
                <div className="font-semibold text-gray-900">
                  {selectedTemplate.content.header}
                </div>
              )}
              <div className="text-gray-800">
                {selectedTemplate.content.body}
              </div>
              {selectedTemplate.content.footer && (
                <div className="text-sm text-gray-600 border-t pt-2">
                  {selectedTemplate.content.footer}
                </div>
              )}
              {selectedTemplate.content.buttons && selectedTemplate.content.buttons.length > 0 && (
                <div className="space-y-2 pt-2">
                  {selectedTemplate.content.buttons.map((button: string, index: number) => (
                    <Button key={index} variant="outline" size="sm" className="w-full">
                      {button}
                    </Button>
                  ))}
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminTemplates;
