
import React, { useState } from 'react';
import { Building2, Filter, Search, Eye, Calendar, TrendingUp, MessageSquare } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

// Mock data
const mockBusinesses = [
  {
    id: 1,
    name: 'TechCorp Solutions',
    signupDate: '2024-01-15',
    plan: 'Enterprise',
    campaigns: 45,
    messages: 12500,
    status: 'Active',
    automations: 8,
    topCategory: 'Marketing',
    usage: 85
  },
  {
    id: 2,
    name: 'Digital Marketing Pro',
    signupDate: '2024-02-10',
    plan: 'Pro',
    campaigns: 23,
    messages: 5800,
    status: 'Active',
    automations: 5,
    topCategory: 'Sales',
    usage: 67
  },
  {
    id: 3,
    name: 'StartupXYZ',
    signupDate: '2024-03-05',
    plan: 'Starter',
    campaigns: 8,
    messages: 1200,
    status: 'Inactive',
    automations: 2,
    topCategory: 'Support',
    usage: 34
  },
  {
    id: 4,
    name: 'Global Retail Co',
    signupDate: '2024-01-28',
    plan: 'Enterprise',
    campaigns: 67,
    messages: 18900,
    status: 'Active',
    automations: 12,
    topCategory: 'E-commerce',
    usage: 92
  }
];

const SuperAdminBusinesses: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPlan, setFilterPlan] = useState('all');
  const [selectedBusiness, setSelectedBusiness] = useState<any>(null);

  const filteredBusinesses = mockBusinesses.filter(business => {
    const matchesSearch = business.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPlan = filterPlan === 'all' || business.plan.toLowerCase() === filterPlan;
    return matchesSearch && matchesPlan;
  });

  const getStatusBadge = (status: string) => {
    return status === 'Active' ? (
      <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">
        Active
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-gray-100 text-gray-600">
        Inactive
      </Badge>
    );
  };

  const getPlanBadge = (plan: string) => {
    const colors = {
      'Free': 'bg-gray-100 text-gray-700',
      'Starter': 'bg-blue-100 text-blue-700',
      'Pro': 'bg-teal-100 text-teal-700',
      'Enterprise': 'bg-purple-100 text-purple-700'
    };
    return (
      <Badge variant="secondary" className={colors[plan as keyof typeof colors] || 'bg-gray-100 text-gray-700'}>
        {plan}
      </Badge>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Business Accounts</h1>
          <p className="text-gray-600 mt-1">Monitor and manage all businesses on the platform</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search businesses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <select
            value={filterPlan}
            onChange={(e) => setFilterPlan(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Plans</option>
            <option value="free">Free</option>
            <option value="starter">Starter</option>
            <option value="pro">Pro</option>
            <option value="enterprise">Enterprise</option>
          </select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Businesses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{mockBusinesses.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Active Businesses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {mockBusinesses.filter(b => b.status === 'Active').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Messages</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {mockBusinesses.reduce((sum, b) => sum + b.messages, 0).toLocaleString()}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Campaigns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {mockBusinesses.reduce((sum, b) => sum + b.campaigns, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Businesses Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Business Directory
          </CardTitle>
          <CardDescription>
            Complete list of businesses registered on the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Business Name</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Signup Date</TableHead>
                <TableHead>Campaigns</TableHead>
                <TableHead>Messages</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredBusinesses.map((business) => (
                <TableRow key={business.id} className="hover:bg-gray-50 transition-colors">
                  <TableCell className="font-medium">{business.name}</TableCell>
                  <TableCell>{getPlanBadge(business.plan)}</TableCell>
                  <TableCell className="text-gray-600">
                    {new Date(business.signupDate).toLocaleDateString()}
                  </TableCell>
                  <TableCell>{business.campaigns}</TableCell>
                  <TableCell>{business.messages.toLocaleString()}</TableCell>
                  <TableCell>{getStatusBadge(business.status)}</TableCell>
                  <TableCell>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => setSelectedBusiness(business)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-lg">
                        <DialogHeader>
                          <DialogTitle>{business.name}</DialogTitle>
                          <DialogDescription>
                            Business insights and usage statistics
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="p-3 bg-gray-50 rounded-lg">
                              <div className="text-sm text-gray-600">Current Plan</div>
                              <div className="font-semibold">{business.plan}</div>
                            </div>
                            <div className="p-3 bg-gray-50 rounded-lg">
                              <div className="text-sm text-gray-600">Status</div>
                              <div className="font-semibold">{business.status}</div>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="p-3 bg-blue-50 rounded-lg">
                              <div className="text-sm text-blue-600">Automations</div>
                              <div className="font-semibold text-blue-700">{business.automations}</div>
                            </div>
                            <div className="p-3 bg-green-50 rounded-lg">
                              <div className="text-sm text-green-600">Top Category</div>
                              <div className="font-semibold text-green-700">{business.topCategory}</div>
                            </div>
                          </div>
                          <div className="p-3 bg-teal-50 rounded-lg">
                            <div className="text-sm text-teal-600">Usage This Month</div>
                            <div className="font-semibold text-teal-700">{business.usage}%</div>
                            <div className="w-full bg-teal-200 rounded-full h-2 mt-2">
                              <div 
                                className="bg-teal-600 h-2 rounded-full transition-all duration-500"
                                style={{ width: `${business.usage}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default SuperAdminBusinesses;
