
import React from 'react';
import { MessageSquare, Users, Send, Zap, TrendingUp, Clock } from 'lucide-react';
import StatsCard from '@/components/common/StatsCard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

// Mock data - TODO: Replace with real API calls
const mockEngagementData = [
  { name: 'Jan', messages: 4000, delivered: 3800, replies: 1200 },
  { name: 'Feb', messages: 3000, delivered: 2900, replies: 980 },
  { name: 'Mar', messages: 2000, delivered: 1950, replies: 850 },
  { name: 'Apr', messages: 2780, delivered: 2700, replies: 920 },
  { name: 'May', messages: 1890, delivered: 1820, replies: 740 },
  { name: 'Jun', messages: 2390, delivered: 2300, replies: 890 },
];

const mockRecentActivity = [
  { id: 1, campaign: 'Welcome Series', status: 'Active', sent: 1250, delivered: 1180, time: '2 hours ago' },
  { id: 2, campaign: 'Product Launch', status: 'Completed', sent: 890, delivered: 856, time: '1 day ago' },
  { id: 3, campaign: 'Holiday Sale', status: 'Scheduled', sent: 0, delivered: 0, time: 'Tomorrow 9:00 AM' },
  { id: 4, campaign: 'Cart Abandonment', status: 'Active', sent: 156, delivered: 149, time: '3 hours ago' },
];

const AdminDashboard: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-1">Welcome back! Here's what's happening with your campaigns.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Messages"
          value="12,584"
          icon={MessageSquare}
          trend={{ value: "12% from last month", isPositive: true }}
          color="teal"
        />
        <StatsCard
          title="Active Contacts"
          value="3,247"
          icon={Users}
          trend={{ value: "8% from last month", isPositive: true }}
          color="blue"
        />
        <StatsCard
          title="Campaigns Sent"
          value="156"
          icon={Send}
          trend={{ value: "24% from last month", isPositive: true }}
          color="green"
        />
        <StatsCard
          title="Active Automations"
          value="23"
          icon={Zap}
          trend={{ value: "3 new this week", isPositive: true }}
          color="purple"
        />
      </div>

      {/* Charts and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Engagement Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              WhatsApp Engagement
            </CardTitle>
            <CardDescription>
              Messages sent, delivered, and replies over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={mockEngagementData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="messages" 
                  stroke="#0d9488" 
                  strokeWidth={2}
                  name="Messages Sent"
                />
                <Line 
                  type="monotone" 
                  dataKey="delivered" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  name="Delivered"
                />
                <Line 
                  type="monotone" 
                  dataKey="replies" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  name="Replies"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Campaign Activity
            </CardTitle>
            <CardDescription>
              Latest updates from your active campaigns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRecentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{activity.campaign}</p>
                    <div className="flex items-center gap-4 mt-1">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        activity.status === 'Active' ? 'bg-green-100 text-green-800' :
                        activity.status === 'Completed' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {activity.status}
                      </span>
                      {activity.sent > 0 && (
                        <span className="text-sm text-gray-600">
                          Sent: {activity.sent} | Delivered: {activity.delivered}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    {activity.time}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
